<template>
  <div class="contract-list">
    <!-- 搜索和筛选 -->
    <div class="list-header">
      <div class="search-section">
        <el-select
          v-model="searchField"
          placeholder="选择搜索字段"
          style="width: 150px"
          @change="handleFieldChange"
        >
          <el-option label="全部字段" value="all_fields" />
          <el-option label="流水号" value="serial_number" />
          <el-option label="合同编号" value="contract_number" />
          <el-option label="文件名" value="filename" />
          <el-option label="提交人" value="submitter_name" />
          <el-option label="审核人" value="reviewer_name" />
          <el-option label="提交备注" value="submit_note" />
          <el-option label="审核意见" value="review_comment" />
        </el-select>

        <el-input
          v-model="searchKeyword"
          :placeholder="getSearchPlaceholder()"
          style="width: 250px"
          clearable
          @keyup.enter="handleSearch"
          @clear="handleClear"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-button type="primary" @click="handleSearch" :loading="loading">
          搜索
        </el-button>

        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          style="width: 150px"
          clearable
          @change="handleFilter"
        >
          <el-option label="全部状态" value="" />
          <el-option label="待审核" value="pending" />
          <el-option label="已通过" value="approved" />
          <el-option label="已拒绝" value="rejected" />
        </el-select>

        <el-button :loading="loading" @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>

      <div class="action-section">
        <slot name="actions"></slot>
      </div>
    </div>

    <!-- 合同表格 -->
    <div class="table-container">
      <el-table
        :data="contracts"
        :loading="loading"
        stripe
        class="clickable-table"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          v-if="showSelection"
          type="selection"
          width="55"
          align="center"
        />

        <el-table-column
          prop="serial_number"
          label="流水号"
          width="140"
          show-overflow-tooltip
        />

        <el-table-column
          prop="contract_number"
          label="合同编号"
          width="160"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span v-if="row.contract_number" class="contract-number">
              {{ row.contract_number }}
            </span>
            <span v-else class="no-contract-number">-</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="filename"
          label="文件名"
          min-width="200"
          show-overflow-tooltip
        />

        <el-table-column
          prop="submitter_name"
          label="提交人"
          width="120"
          show-overflow-tooltip
        />

        <el-table-column
          prop="reviewer_name"
          label="审核人"
          width="120"
          show-overflow-tooltip
        />

        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="{ row }">
            <div class="status-cell">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ formatStatus(row.status) }}
              </el-tag>
              <el-tooltip
                v-if="row.review_comment"
                :content="row.review_comment"
                placement="top"
                :show-after="500"
              >
                <el-icon
                  class="comment-icon"
                  :class="`comment-icon--${row.status}`"
                >
                  <ChatDotRound />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="file_size"
          label="文件大小"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            {{ formatFileSize(row.file_size) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="created_at"
          label="提交时间"
          width="160"
          align="center"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="reviewed_at"
          label="审核时间"
          width="160"
          align="center"
        >
          <template #default="{ row }">
            <span v-if="row.reviewed_at">
              {{ formatDateTime(row.reviewed_at) }}
            </span>
            <span v-else class="not-reviewed">-</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" min-width="120" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-if="row.permissions?.canView || canView(row)"
                type="primary"
                size="small"
                text
                @click.stop="viewContractInTab(row)"
              >
                查看
              </el-button>

              <el-button
                v-if="row.permissions?.canModify || canModify(row)"
                type="warning"
                size="small"
                text
                @click.stop="editContract(row)"
              >
                修改
              </el-button>

              <el-button
                v-if="row.permissions?.canReview || canReview(row)"
                type="success"
                size="small"
                text
                @click.stop="reviewContract(row)"
              >
                审核
              </el-button>

              <el-button
                v-if="row.permissions?.canDelete || canDelete(row)"
                type="danger"
                size="small"
                text
                @click.stop="deleteContract(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="isEmpty" class="empty-state">
      <el-icon :size="64" class="empty-icon">
        <DocumentCopy />
      </el-icon>
      <h3 class="empty-title">暂无合同数据</h3>
      <p class="empty-description">
        {{ getEmptyDescription() }}
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Search,
  Refresh,
  DocumentCopy,
  ChatDotRound,
} from "@element-plus/icons-vue";

import { useContracts } from "@/composables/useContracts";
import { useAuth } from "@/composables/useAuth";
import { contractUtils, contractsAPI } from "@/api/contracts";

// 定义 props
const props = defineProps({
  showSelection: {
    type: Boolean,
    default: false,
  },
  filterRole: {
    type: String,
    default: "", // 'employee', 'reviewer', 'admin'
  },
});

// 定义 emits
const emit = defineEmits([
  "view-contract",
  "view-contract-tab",
  "edit-contract",
  "review-contract",
  "delete-contract",
  "selection-change",
  "row-click",
]);

// 认证和权限
const { user, userRole } = useAuth();

// 合同管理
const {
  loading,
  contracts,
  pagination,
  getContracts,
  deleteContract: deleteContractAPI,
  updateStatsAfterDelete,
  canModify,
  canReview,
  canView,
  formatStatus,
  getStatusColor,
  formatFileSize,
  formatDateTime,
  isEmpty,
} = useContracts();

// 搜索和筛选
const searchKeyword = ref("");
const searchField = ref("all_fields");
const statusFilter = ref("");

// 分页数据
const currentPage = computed({
  get: () => pagination.value.page,
  set: (value) => setPagination(value, pageSize.value),
});

const pageSize = computed({
  get: () => pagination.value.pageSize,
  set: (value) => setPagination(currentPage.value, value),
});

const total = computed(() => pagination.value.total);

// 选中的行
const selectedRows = ref([]);

// 检查删除权限 - 使用统一的权限检查逻辑
const canDelete = (contract) => {
  return contractUtils.canDelete(contract, user.value);
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1; // 重置到第一页
  loadContracts();
};

// 处理字段变化
const handleFieldChange = () => {
  // 字段变化时如果有搜索词则立即搜索
  if (searchKeyword.value.trim()) {
    handleSearch();
  }
};

// 处理清空搜索
const handleClear = () => {
  searchKeyword.value = "";
  searchField.value = "all_fields";
  currentPage.value = 1;
  loadContracts();
};

// 获取搜索提示文本
const getSearchPlaceholder = () => {
  const placeholders = {
    all_fields: "搜索所有字段",
    serial_number: "输入流水号，如：HT001",
    contract_number: "输入合同编号",
    filename: "输入文件名",
    submitter_name: "输入提交人姓名",
    reviewer_name: "输入审核人姓名",
    submit_note: "输入提交备注内容",
    review_comment: "输入审核意见内容"
  };
  return placeholders[searchField.value] || "请输入搜索关键词";
};

// 处理筛选
const handleFilter = () => {
  loadContracts();
};

// 刷新列表
const refreshList = () => {
  loadContracts();
};

// 加载合同列表
const loadContracts = async () => {
  const params = {};

  if (searchKeyword.value && searchKeyword.value.trim()) {
    params.keyword = searchKeyword.value.trim();
    params.searchField = searchField.value;
  }

  if (statusFilter.value) {
    params.status = statusFilter.value;
  }

  // 根据角色筛选
  if (props.filterRole === "employee") {
    params.my = true;
  }
  // 审核员的权限过滤由后端自动处理，无需前端指定reviewer_id

  await getContracts(params);
};

// 查看合同（弹窗方式）
const viewContract = (contract) => {
  emit("view-contract", contract);
};

// 查看合同（Tab方式）
const viewContractInTab = (contract) => {
  emit("view-contract-tab", contract);
};

// 编辑合同
const editContract = (contract) => {
  emit("edit-contract", contract);
};

// 审核合同
const reviewContract = (contract) => {
  emit("review-contract", contract);
};

// 删除合同
const deleteContract = async (contract) => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      `确定要删除合同 ${contract.serial_number} 吗？删除后无法恢复。`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    // 调用删除API
    const response = await contractsAPI.delete(contract.id);

    if (response.success) {
      ElMessage.success(`合同 ${contract.serial_number} 删除成功`);

      // 🔥 关键修改：直接从本地contracts数组中移除已删除的合同
      // 这样避免了依赖可能被缓存的API响应
      const contractIndex = contracts.value.findIndex(
        (c) => c.id === contract.id,
      );
      if (contractIndex !== -1) {
        contracts.value.splice(contractIndex, 1);
      }

      // 更新分页信息
      if (pagination.value.total > 0) {
        pagination.value.total -= 1;
      }

      // 🔥 新增：更新本地统计数据，避免API调用
      updateStatsAfterDelete(contract);

      console.log(
        "📋 当前列表中的合同:",
        contracts.value.map((c) => `${c.serial_number}(ID:${c.id})`),
      );

      // 触发删除事件，通知父组件（传递更新后的统计数据）
      emit("delete-contract", contract);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除合同失败:", error);
      ElMessage.error(error.message || "删除合同失败");
    }
  }
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
  emit("selection-change", selection);
};

// 处理行点击 - 直接跳转到查看页面
const handleRowClick = (row) => {
  viewContractInTab(row);
  emit("row-click", row);
};

// 处理页面大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  loadContracts();
};

// 处理当前页变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  loadContracts();
};

// 设置分页
const setPagination = () => {
  // 这个方法在 useContracts 中实现
};

// 获取空状态描述
const getEmptyDescription = () => {
  if (searchKeyword.value.trim() || statusFilter.value) {
    return "没有找到符合条件的合同，请尝试调整搜索条件";
  }

  switch (props.filterRole) {
    case "employee":
      return '您还没有提交任何合同，点击"提交合同"开始';
    case "reviewer":
      return "暂无分配给您的合同需要审核";
    default:
      return "系统中暂无合同数据";
  }
};

// 监听状态筛选条件变化
watch(statusFilter, () => {
  currentPage.value = 1;
  loadContracts();
});

// 组件挂载时加载数据
onMounted(() => {
  loadContracts();
});

// 暴露方法
defineExpose({
  refreshList,
  handleFilter,
  statusFilter,
  selectedRows: computed(() => selectedRows.value),
});
</script>

<style scoped>
.contract-list {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.search-section {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.action-section {
  display: flex;
  gap: 12px;
}

.table-container {
  overflow-x: auto;
}

/* 让表格行可点击 */
.clickable-table :deep(.el-table__row) {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clickable-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa !important;
}

.action-buttons {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding: 0 4px;
  min-width: fit-content;
  width: 100%;
}

.action-buttons .el-button {
  flex: 1;
  margin: 0 2px;
  min-width: 50px;
  max-width: 70px;
}

.pagination-container {
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e4e7ed;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  color: #303133;
  margin: 0 0 8px;
}

.empty-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 状态单元格 */
.status-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.comment-icon {
  font-size: 14px;
  cursor: help;
  transition: all 0.2s;
}

.comment-icon:hover {
  transform: scale(1.1);
}

.comment-icon--approved {
  color: #67c23a;
}

.comment-icon--rejected {
  color: #f56c6c;
}

/* 未审核状态 */
.not-reviewed {
  color: #c0c4cc;
  font-style: italic;
}

/* 合同编号样式 */
.contract-number {
  font-weight: 500;
  color: #409eff;
}

.no-contract-number {
  color: #c0c4cc;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-section {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
